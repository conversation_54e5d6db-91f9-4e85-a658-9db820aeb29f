import {fileURLToPath, URL} from 'node:url'
import {readFileSync, writeFileSync, unlinkSync, existsSync} from 'fs'
import {resolve} from 'path'

import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {NaiveUiResolver} from "unplugin-vue-components/resolvers";
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {viteSingleFile} from "vite-plugin-singlefile";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        vue(),
        viteSingleFile({
            // 删除内联的CSS和JS注释
            removeViteModuleLoader: true,
        }),
        // 自定义插件来处理public目录下的文件
        {
            name: 'inline-public-files',
            transformIndexHtml: {
                order: 'post',
                handler(html, ctx) {
                }
            },
            writeBundle(options, bundle) {
                try {
                    const distDir = options.dir || 'dist';

                    // 处理game-only.html文件
                    const gameOnlyPath = resolve(distDir, 'game-only.html');
                    if (existsSync(gameOnlyPath)) {
                        const wxJsPath = resolve(__dirname, 'public/wx.js');
                        const wxJsContent = readFileSync(wxJsPath, 'utf-8');
                        let gameOnlyContent = readFileSync(gameOnlyPath, 'utf-8');

                        // 替换<script src="wx.js"></script>为内联脚本
                        gameOnlyContent = gameOnlyContent.replace(
                            '<script src="wx.js"></script>',
                            `<script>\n${wxJsContent}\n</script>`
                        );

                        writeFileSync(gameOnlyPath, gameOnlyContent);
                        console.log('已将wx.js内联到game-only.html');
                    }

                    // 删除多余的wx.js文件
                    const wxJsPath = resolve(distDir, 'wx.js');
                    if (existsSync(wxJsPath)) {
                        unlinkSync(wxJsPath);
                        console.log('已删除多余的wx.js文件');
                    }
                } catch (error) {
                    console.warn('处理game-only.html时出错:', error);
                }
            }
        },
        AutoImport({
            // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
            imports: [
                'vue',
                {
                    'naive-ui': [
                        'useDialog',
                        'useMessage',
                        'useNotification',
                        'useLoadingBar',
                    ],
                },
            ],
            dts: './auto-imports.d.ts',
            eslintrc: {
                enabled: false, // 1、true时生成eslint配置文件，2、生成后改为false，避免重复消耗
            },
        }),
        Components({
            resolvers: [NaiveUiResolver()],
        }),
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    },
    // 配置public目录处理
    publicDir: 'public',
    build: {
        // 排除wx.js文件，因为我们已经内联了
        rollupOptions: {
            external: [],
        }
    }
})
